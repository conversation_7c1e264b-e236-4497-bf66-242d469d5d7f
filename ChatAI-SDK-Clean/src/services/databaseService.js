// Database service for ChatAI-SDK-Clean
const { DataSource } = require('typeorm');
const { databaseConfig } = require('../config/database');

// Import entities
const ChatAiDocument = require('../entities/ChatAiDocument');
const ChatAiMessage = require('../entities/ChatAiMessage');
const ChatAi = require('../entities/ChatAi');

class DatabaseService {
  constructor() {
    this.dataSource = null;
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxRetries = 3;
  }

  /**
   * Initialize database connection
   */
  async initialize() {
    try {
      console.log('🔌 Initializing database connection...');

      this.dataSource = new DataSource({
        ...databaseConfig,
        entities: [ChatAiDocument, ChatAiMessage, ChatAi],
      });

      await this.dataSource.initialize();
      this.isConnected = true;
      this.connectionAttempts = 0;

      console.log('✅ Database connection established successfully');
      console.log(`📊 Connected to: ${databaseConfig.host}:${databaseConfig.port}/${databaseConfig.database}`);

      // Test connection with a simple query
      await this.testConnection();

    } catch (error) {
      this.connectionAttempts++;
      console.error(`❌ Database connection failed (attempt ${this.connectionAttempts}/${this.maxRetries}):`, error.message);

      if (this.connectionAttempts < this.maxRetries) {
        console.log(`🔄 Retrying connection in 3 seconds...`);
        setTimeout(() => this.initialize(), 3000);
      } else {
        throw new Error(`Failed to connect to database after ${this.maxRetries} attempts: ${error.message}`);
      }
    }
  }

  /**
   * Test database connection
   */
  async testConnection() {
    try {
      const result = await this.dataSource.query('SELECT NOW() as current_time');
      console.log(`🕒 Database time: ${result[0].current_time}`);
      return true;
    } catch (error) {
      console.error('❌ Database connection test failed:', error.message);
      throw error;
    }
  }

  /**
   * Update document status with additional data
   */
  async updateDocumentStatus(documentId, appId, status, additionalData = {}) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const documentRepo = this.dataSource.getRepository('ChatAiDocument');

      // Find document with security validation (appId check)
      const document = await documentRepo
        .createQueryBuilder('document')
        .leftJoin('document.project', 'project')
        .where('document.id = :documentId', { documentId })
        .andWhere('project.appId = :appId', { appId })
        .getOne();

      if (!document) {
        throw new Error(`Document not found or access denied: ${documentId} for app ${appId}`);
      }

      // Prepare update data (only use fields that exist in the schema)
      const updateFields = {
        status,
        ...additionalData,
      };

      // Remove any fields that don't exist in the actual schema
      delete updateFields.message; // This field doesn't exist in the database
      delete updateFields.lastUpdated;
      delete updateFields.processingStartedAt;
      delete updateFields.processingCompletedAt;
      delete updateFields.chunkCount;
      delete updateFields.vectorCount;
      delete updateFields.processingTimeMs;
      delete updateFields.filename;
      delete updateFields.filesize;
      delete updateFields.contentType;

      // Update document
      const result = await documentRepo.update(documentId, updateFields);

      if (result.affected === 0) {
        throw new Error(`No document updated for ID: ${documentId}`);
      }

      console.log(`✅ Document status updated: ${documentId} → ${status}`);

      // Log additional data if present
      if (Object.keys(additionalData).length > 0) {
        const logData = { ...additionalData };
        // Don't log large data fields
        if (logData.parsedData) logData.parsedData = '[PARSED_DATA]';
        console.log(`📊 Additional data:`, JSON.stringify(logData, null, 2));
      }

      return {
        success: true,
        documentId,
        status,
        updatedFields: Object.keys(updateFields),
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error(`❌ Database update failed for document ${documentId}:`, error.message);
      throw error;
    }
  }

  /**
   * Save chat message
   */
  async saveChatMessage(messageData) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const messageRepo = this.dataSource.getRepository('ChatAiMessage');

      // Validate required fields
      const requiredFields = ['question', 'response', 'chatAiId', 'userId'];
      const missingFields = requiredFields.filter(field => !messageData[field]);

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      // Create message with timestamp
      const messageToSave = {
        ...messageData,
        timestamp: new Date(),
      };

      const message = messageRepo.create(messageToSave);
      const savedMessage = await messageRepo.save(message);

      console.log(`✅ Chat message saved: ${savedMessage.id} (session: ${savedMessage.sessionId || 'none'})`);
      return savedMessage;

    } catch (error) {
      console.error(`❌ Failed to save chat message:`, error.message);
      throw error;
    }
  }

  /**
   * Get chat history for a ChatAI project
   */
  async getChatHistory(chatAiId, userId, limit = 50, sessionId = null) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const messageRepo = this.dataSource.getRepository('ChatAiMessage');

      const queryBuilder = messageRepo
        .createQueryBuilder('message')
        .where('message.chatAiId = :chatAiId', { chatAiId })
        .andWhere('message.userId = :userId', { userId });

      if (sessionId) {
        queryBuilder.andWhere('message.sessionId = :sessionId', { sessionId });
      }

      const messages = await queryBuilder
        .orderBy('message.timestamp', 'DESC')
        .limit(limit)
        .getMany();

      console.log(`📜 Retrieved ${messages.length} chat messages for ChatAI ${chatAiId}`);
      return messages.reverse(); // Return in chronological order

    } catch (error) {
      console.error(`❌ Failed to get chat history:`, error.message);
      throw error;
    }
  }

  /**
   * Get document by ID with validation
   */
  async getDocument(documentId, appId) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const documentRepo = this.dataSource.getRepository('ChatAiDocument');

      const document = await documentRepo
        .createQueryBuilder('document')
        .leftJoin('document.project', 'project')
        .where('document.id = :documentId', { documentId })
        .andWhere('project.appId = :appId', { appId })
        .getOne();

      if (!document) {
        throw new Error(`Document not found: ${documentId} for app ${appId}`);
      }

      return document;

    } catch (error) {
      console.error(`❌ Failed to get document:`, error.message);
      throw error;
    }
  }

  /**
   * Get documents by status
   */
  async getDocumentsByStatus(appId, status, limit = 100) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const documentRepo = this.dataSource.getRepository('ChatAiDocument');

      const documents = await documentRepo
        .createQueryBuilder('document')
        .leftJoin('document.project', 'project')
        .where('project.appId = :appId', { appId })
        .andWhere('document.status = :status', { status })
        .orderBy('document.createdAt', 'DESC')
        .limit(limit)
        .getMany();

      return documents;

    } catch (error) {
      console.error(`❌ Failed to get documents by status:`, error.message);
      throw error;
    }
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      if (!this.isConnected) {
        return { status: 'disconnected', error: 'Database not connected' };
      }

      await this.testConnection();

      return {
        status: 'healthy',
        connected: true,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        connected: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Close database connection
   */
  async close() {
    try {
      if (this.dataSource && this.isConnected) {
        await this.dataSource.destroy();
        this.isConnected = false;
        console.log('✅ Database connection closed gracefully');
      }
    } catch (error) {
      console.error('❌ Error closing database connection:', error.message);
    }
  }

  /**
   * Atomically deduct credits for queries to prevent race conditions
   * Uses database-level atomic operations for concurrency safety
   */
  async deductQueryCredits(chatAiId, userId, creditsToDeduct = 1, queryDetails = {}) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      console.log(`💰 Attempting to deduct ${creditsToDeduct} credits for ChatAI: ${chatAiId}`);

      // First check if the ChatAI project exists and get subscription status
      const chatAiRepo = this.dataSource.getRepository('ChatAi');
      const chatAi = await chatAiRepo.findOne({
        where: { id: chatAiId },
        select: ['id', 'subscriptionStatus', 'credits', 'userId'],
      });

      if (!chatAi) {
        console.warn(`❌ ChatAI project not found: ${chatAiId}`);
        return {
          success: false,
          message: 'ChatAI project not found',
          creditsRemaining: 0,
        };
      }

      // Verify user ownership
      if (chatAi.userId !== userId) {
        console.warn(`❌ User ${userId} does not own ChatAI project ${chatAiId}`);
        return {
          success: false,
          message: 'Access denied: User does not own this ChatAI project',
          creditsRemaining: 0,
        };
      }

      // Pro and enterprise users don't deduct credits
      if (chatAi.subscriptionStatus === 'pro' || chatAi.subscriptionStatus === 'enterprise') {
        console.log(`✅ Premium user (${chatAi.subscriptionStatus}): No credit deduction needed`);

        // Log usage for analytics (optional)
        await this.logCreditUsage(chatAiId, userId, queryDetails.queryType || 'chat_query', 0, queryDetails);

        return {
          success: true,
          message: 'Premium user - unlimited queries',
          creditsRemaining: chatAi.credits,
          subscriptionStatus: chatAi.subscriptionStatus,
          isPremium: true,
        };
      }

      // For free users: Use atomic UPDATE to prevent race conditions
      // This single query atomically checks credits and deducts if sufficient
      const result = await this.dataSource
        .createQueryBuilder()
        .update('ChatAi')
        .set({
          credits: () => `credits - ${creditsToDeduct}`,
        })
        .where('id = :chatAiId', { chatAiId })
        .andWhere('credits >= :creditsToDeduct', { creditsToDeduct })
        .andWhere('subscriptionStatus = :status', { status: 'free' })
        .execute();

      // Check if the update was successful (affected rows > 0)
      const creditsDeducted = result.affected > 0;

      if (creditsDeducted) {
        // Get updated credit balance
        const updatedChatAi = await chatAiRepo.findOne({
          where: { id: chatAiId },
          select: ['credits'],
        });

        // Log successful credit usage
        await this.logCreditUsage(chatAiId, userId, queryDetails.queryType || 'chat_query', creditsToDeduct, queryDetails);

        console.log(`✅ Credits deducted successfully: ${creditsToDeduct} credits for user ${userId}`);
        console.log(`💰 Remaining credits: ${updatedChatAi?.credits || 0}`);

        return {
          success: true,
          message: 'Credits deducted successfully',
          creditsRemaining: updatedChatAi?.credits || 0,
          subscriptionStatus: 'free',
          isPremium: false,
        };
      } else {
        console.warn(`❌ Credit deduction failed: Insufficient credits for user ${userId}`);

        return {
          success: false,
          message: `Insufficient credits for query. You need ${creditsToDeduct} credits but have ${chatAi.credits}. Upgrade to Pro for unlimited queries!`,
          creditsRemaining: chatAi.credits,
          subscriptionStatus: 'free',
          isPremium: false,
        };
      }

    } catch (error) {
      console.error(`❌ Error during credit deduction for user ${userId}:`, error.message);
      return {
        success: false,
        message: 'Failed to process credit deduction',
        error: error.message,
      };
    }
  }

  /**
   * Log credit usage for analytics and audit trail
   */
  async logCreditUsage(chatAiId, userId, actionType, creditsUsed, queryDetails = {}) {
    try {
      // This would typically insert into a credit_usage table
      // For now, we'll just log to console for analytics
      const logEntry = {
        chatAiId,
        userId,
        actionType,
        creditsUsed,
        timestamp: new Date().toISOString(),
        queryDetails: {
          queryType: queryDetails.queryType,
          sessionId: queryDetails.sessionId,
          estimatedTokens: queryDetails.estimatedTokens,
        },
      };

      console.log(`📊 Credit Usage Log:`, JSON.stringify(logEntry, null, 2));

      // TODO: If you have a credit_usage table, insert the record here
      // const creditUsageRepo = this.dataSource.getRepository('CreditUsage');
      // await creditUsageRepo.save(logEntry);

    } catch (error) {
      console.warn(`⚠️ Failed to log credit usage:`, error.message);
      // Don't throw error - logging failure shouldn't break the main flow
    }
  }

  /**
   * Check if user can make a query (pre-flight check)
   */
  async canUserMakeQuery(chatAiId, userId, creditsRequired = 1) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const chatAiRepo = this.dataSource.getRepository('ChatAi');
      const chatAi = await chatAiRepo.findOne({
        where: { id: chatAiId },
        select: ['credits', 'subscriptionStatus', 'userId'],
      });

      if (!chatAi) {
        return {
          canProceed: false,
          message: 'ChatAI project not found',
          creditsRemaining: 0,
        };
      }

      // Verify user ownership
      if (chatAi.userId !== userId) {
        return {
          canProceed: false,
          message: 'Access denied: User does not own this ChatAI project',
          creditsRemaining: 0,
        };
      }

      // Premium users can always proceed
      if (chatAi.subscriptionStatus === 'pro' || chatAi.subscriptionStatus === 'enterprise') {
        return {
          canProceed: true,
          message: 'Premium user - unlimited queries available',
          creditsRemaining: chatAi.credits,
          subscriptionStatus: chatAi.subscriptionStatus,
          isPremium: true,
        };
      }

      // Free users need sufficient credits
      const canProceed = chatAi.credits >= creditsRequired;

      return {
        canProceed,
        message: canProceed
          ? `${chatAi.credits} credits available`
          : `Insufficient credits. Need ${creditsRequired}, have ${chatAi.credits}`,
        creditsRemaining: chatAi.credits,
        subscriptionStatus: chatAi.subscriptionStatus,
        isPremium: false,
      };

    } catch (error) {
      console.error(`❌ Error checking query permissions:`, error.message);
      return {
        canProceed: false,
        message: 'Failed to check query permissions',
        error: error.message,
      };
    }
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      connected: this.isConnected,
      attempts: this.connectionAttempts,
      maxRetries: this.maxRetries,
    };
  }

  /**
   * Get database connection (for direct queries when needed)
   */
  getDataSource() {
    return this.dataSource;
  }
}

// Export singleton instance
module.exports = new DatabaseService();
