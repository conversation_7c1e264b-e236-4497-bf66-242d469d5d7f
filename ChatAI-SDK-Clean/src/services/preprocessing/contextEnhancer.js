/**
 * Context Enhancer
 * Enhances user queries by adding missing context from conversation history
 */

class ContextEnhancer {
  constructor() {
    // Patterns for context resolution
    this.pronounPatterns = {
      'it': /\bit\b/gi,
      'this': /\bthis\b/gi,
      'that': /\bthat\b/gi,
      'they': /\bthey\b/gi,
      'them': /\bthem\b/gi,
      'these': /\bthese\b/gi,
      'those': /\bthose\b/gi
    };

    this.referencePatterns = {
      'above': /\babove\b/gi,
      'previous': /\bprevious\b/gi,
      'earlier': /\bearlier\b/gi,
      'before': /\bbefore\b/gi,
      'last': /\blast\b/gi
    };

    // Common follow-up patterns
    this.followUpPatterns = {
      'also': /\balso\b/gi,
      'too': /\btoo\b/gi,
      'additionally': /\badditionally\b/gi,
      'furthermore': /\bfurthermore\b/gi,
      'and': /^and\b/gi,
      'plus': /\bplus\b/gi
    };
  }

  /**
   * Enhance query with conversation context
   * @param {string} query - Original user query
   * @param {Array} recentHistory - Recent conversation history
   * @param {Object} analysis - Query analysis results
   * @returns {Object} Enhancement results
   */
  async enhanceWithContext(query, recentHistory = [], analysis = {}) {
    if (!analysis.needsContext || recentHistory.length === 0) {
      return {
        enhanced: query,
        refinements: [],
        contextUsed: false
      };
    }

    const refinements = [];
    let enhancedQuery = query;

    try {
      // Extract context from recent history
      const context = this.extractRelevantContext(recentHistory);
      
      // Resolve pronouns and references
      const pronounResolution = this.resolvePronouns(enhancedQuery, context);
      if (pronounResolution.enhanced !== enhancedQuery) {
        enhancedQuery = pronounResolution.enhanced;
        refinements.push(...pronounResolution.refinements);
      }

      // Handle follow-up queries
      const followUpResolution = this.resolveFollowUps(enhancedQuery, context);
      if (followUpResolution.enhanced !== enhancedQuery) {
        enhancedQuery = followUpResolution.enhanced;
        refinements.push(...followUpResolution.refinements);
      }

      // Add topic continuity if needed
      const topicContinuity = this.addTopicContinuity(enhancedQuery, context, analysis);
      if (topicContinuity.enhanced !== enhancedQuery) {
        enhancedQuery = topicContinuity.enhanced;
        refinements.push(...topicContinuity.refinements);
      }

      console.log(`🔗 Context enhancement completed: ${refinements.length} refinements applied`);

      return {
        enhanced: enhancedQuery,
        refinements,
        contextUsed: true,
        contextSources: context.sources
      };

    } catch (error) {
      console.error(`❌ Error in context enhancement:`, error);
      return {
        enhanced: query,
        refinements: [],
        contextUsed: false,
        error: error.message
      };
    }
  }

  /**
   * Extract relevant context from conversation history
   */
  extractRelevantContext(history) {
    const context = {
      topics: [],
      entities: [],
      lastQuery: null,
      lastResponse: null,
      sources: []
    };

    // Process recent history (most recent first)
    for (let i = history.length - 1; i >= 0; i--) {
      const entry = history[i];
      
      if (entry.role === 'user') {
        if (!context.lastQuery) {
          context.lastQuery = entry.content;
        }
        
        // Extract entities and topics from user queries
        const entities = this.extractEntities(entry.content);
        context.entities.push(...entities);
        
        const topics = this.extractTopics(entry.content);
        context.topics.push(...topics);
        
        context.sources.push(`user_query_${i}`);
      } else if (entry.role === 'assistant') {
        if (!context.lastResponse) {
          context.lastResponse = entry.content;
        }
        context.sources.push(`assistant_response_${i}`);
      }
    }

    // Remove duplicates and keep most relevant
    context.entities = [...new Set(context.entities)].slice(0, 5);
    context.topics = [...new Set(context.topics)].slice(0, 3);

    return context;
  }

  /**
   * Resolve pronouns using conversation context
   */
  resolvePronouns(query, context) {
    let enhanced = query;
    const refinements = [];

    // Simple pronoun resolution based on recent entities
    if (context.entities.length > 0) {
      const mostRecentEntity = context.entities[0];
      
      for (const [pronoun, pattern] of Object.entries(this.pronounPatterns)) {
        if (pattern.test(enhanced)) {
          const resolved = enhanced.replace(pattern, mostRecentEntity);
          if (resolved !== enhanced) {
            enhanced = resolved;
            refinements.push({
              type: 'pronoun_resolution',
              original: pronoun,
              resolved: mostRecentEntity,
              confidence: 0.7
            });
          }
        }
      }
    }

    return { enhanced, refinements };
  }

  /**
   * Resolve follow-up queries
   */
  resolveFollowUps(query, context) {
    let enhanced = query;
    const refinements = [];

    // Check if this is a follow-up query
    const isFollowUp = Object.values(this.followUpPatterns).some(pattern => pattern.test(query));
    
    if (isFollowUp && context.lastQuery) {
      // Extract the main topic from the last query
      const lastTopics = this.extractTopics(context.lastQuery);
      
      if (lastTopics.length > 0) {
        const mainTopic = lastTopics[0];
        
        // Add context to the beginning of the query
        enhanced = `Regarding ${mainTopic}, ${enhanced}`;
        
        refinements.push({
          type: 'follow_up_resolution',
          addedContext: mainTopic,
          confidence: 0.8
        });
      }
    }

    return { enhanced, refinements };
  }

  /**
   * Add topic continuity to maintain conversation flow
   */
  addTopicContinuity(query, context, analysis) {
    let enhanced = query;
    const refinements = [];

    // For very short or ambiguous queries, add topic context
    if ((analysis.ambiguity > 0.6 || analysis.completeness < 0.5) && context.topics.length > 0) {
      const relevantTopic = context.topics[0];
      
      // Only add if the query doesn't already contain the topic
      if (!query.toLowerCase().includes(relevantTopic.toLowerCase())) {
        enhanced = `${enhanced} (in the context of ${relevantTopic})`;
        
        refinements.push({
          type: 'topic_continuity',
          addedTopic: relevantTopic,
          confidence: 0.6
        });
      }
    }

    return { enhanced, refinements };
  }

  /**
   * Extract entities from text (simple implementation)
   */
  extractEntities(text) {
    const entities = [];
    
    // Extract capitalized words (potential proper nouns)
    const capitalizedWords = text.match(/\b[A-Z][a-z]+\b/g) || [];
    entities.push(...capitalizedWords);
    
    // Extract quoted terms
    const quotedTerms = text.match(/"([^"]+)"/g) || [];
    entities.push(...quotedTerms.map(q => q.replace(/"/g, '')));
    
    // Extract technical terms (words with dots, underscores, etc.)
    const technicalTerms = text.match(/\b\w+[._]\w+\b/g) || [];
    entities.push(...technicalTerms);
    
    return entities.filter(entity => entity.length > 2).slice(0, 5);
  }

  /**
   * Extract topics from text (simple implementation)
   */
  extractTopics(text) {
    const topics = [];
    
    // Common topic indicators
    const topicPatterns = [
      /about\s+(\w+(?:\s+\w+){0,2})/gi,
      /regarding\s+(\w+(?:\s+\w+){0,2})/gi,
      /concerning\s+(\w+(?:\s+\w+){0,2})/gi,
      /\b(\w+(?:\s+\w+){0,2})\s+(?:issue|problem|question|topic)/gi
    ];
    
    for (const pattern of topicPatterns) {
      const matches = [...text.matchAll(pattern)];
      topics.push(...matches.map(match => match[1].trim()));
    }
    
    // If no explicit topics found, use key nouns
    if (topics.length === 0) {
      const nouns = text.match(/\b[a-z]+(?:tion|ment|ness|ity|ing|ed)\b/gi) || [];
      topics.push(...nouns.slice(0, 2));
    }
    
    return topics.filter(topic => topic.length > 3).slice(0, 3);
  }
}

module.exports = ContextEnhancer;
