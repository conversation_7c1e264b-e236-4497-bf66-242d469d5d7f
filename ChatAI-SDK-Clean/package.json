{"name": "chatai-sdk-clean", "version": "1.0.0", "description": "Simplified ChatAI SDK with single endpoint and optimized performance", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "node test-clean.js"}, "dependencies": {"@qdrant/js-client-rest": "^1.14.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "form-data": "^4.0.3", "helmet": "^7.1.0", "morgan": "^1.10.0", "multer": "^2.0.1", "node-fetch": "^2.7.0", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.25", "uuid": "^9.0.1"}, "devDependencies": {"@types/pg": "^8.15.4", "jest": "^29.7.0", "nodemon": "^3.0.2"}, "keywords": ["chatai", "sdk", "llamaindex", "openrouter", "rag", "express"], "author": "ChatAI Team", "license": "MIT"}