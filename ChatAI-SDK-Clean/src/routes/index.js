const express = require('express');
const fetch = require('node-fetch');
const rateLimit = require('../middleware/rateLimit');
const { chatRateLimit } = require('../middleware/rateLimit');

const router = express.Router();

// Import vector processing routes
const vectorProcessingRoutes = require('./vectorProcessing');

// Import semantic refinement service
const PromptPreprocessorService = require('../services/promptPreprocessorService');

// Initialize semantic refinement service
const promptPreprocessor = new PromptPreprocessorService();

// Apply general rate limiting to all routes
router.use(rateLimit);

// Apply stricter chat rate limiting to the main API endpoint
router.use('/api/v1/', chatRateLimit);

/**
 * Generate dynamic out-of-context response based on available documents
 * @param {Array} documents - Available documents from user service
 * @returns {string} Personalized out-of-context response
 */
function generateDynamicOutOfContextResponse(documents) {
  if (!documents || documents.length === 0) {
    return "I apologize, but I don't have any documents in my current knowledge base. Please upload some documents first, or contact support if you need assistance.";
  }

  // Extract document topics and titles
  const documentInfo = analyzeDocumentTopics(documents);

  if (documentInfo.topics.length === 0) {
    // Fallback to filenames if no topics detected
    const filenames = documents
      .map(doc => doc.filename || 'Unknown Document')
      .filter(name => name !== 'Unknown Document')
      .slice(0, 3); // Limit to first 3 for readability

    if (filenames.length > 0) {
      const fileList = filenames.length === 1
        ? filenames[0]
        : filenames.length === 2
          ? `${filenames[0]} and ${filenames[1]}`
          : `${filenames.slice(0, -1).join(', ')}, and ${filenames[filenames.length - 1]}`;

      return `I apologize, but I don't have information about that topic in my current knowledge base. Please ask questions related to ${fileList}, or contact support if you need assistance with other topics.`;
    }
  } else {
    // Use detected topics
    const topicList = documentInfo.topics.length === 1
      ? documentInfo.topics[0]
      : documentInfo.topics.length === 2
        ? `${documentInfo.topics[0]} and ${documentInfo.topics[1]}`
        : `${documentInfo.topics.slice(0, -1).join(', ')}, and ${documentInfo.topics[documentInfo.topics.length - 1]}`;

    return `I apologize, but I don't have information about that topic in my current knowledge base. Please ask questions related to ${topicList}, or contact support if you need assistance with other topics.`;
  }

  // Final fallback
  return "I apologize, but I don't have information about that topic in my current knowledge base. Please ask questions related to the documents that have been provided, or contact support if you need assistance with other topics.";
}

/**
 * Analyze documents to extract topics and themes
 * @param {Array} documents - Document objects
 * @returns {Object} Analysis results with topics and themes
 */
function analyzeDocumentTopics(documents) {
  const topics = new Set();
  const keywords = new Set();

  documents.forEach(doc => {
    // Extract from filename
    if (doc.filename) {
      const filenameTopics = extractTopicsFromFilename(doc.filename);
      filenameTopics.forEach(topic => topics.add(topic));
    }

    // Extract from document content if available
    if (doc.parsedData && doc.parsedData.text) {
      const contentTopics = extractTopicsFromContent(doc.parsedData.text);
      contentTopics.forEach(topic => topics.add(topic));
    }

    // Extract from metadata if available
    if (doc.metadata) {
      const metadataTopics = extractTopicsFromMetadata(doc.metadata);
      metadataTopics.forEach(topic => topics.add(topic));
    }
  });

  return {
    topics: Array.from(topics).slice(0, 3), // Limit to 3 most relevant topics
    keywords: Array.from(keywords)
  };
}

/**
 * Extract topics from filename
 * @param {string} filename - Document filename
 * @returns {Array} Extracted topics
 */
function extractTopicsFromFilename(filename) {
  const topics = [];

  // Remove file extension and clean up
  const cleanName = filename
    .replace(/\.[^/.]+$/, '')
    .replace(/[-_]/g, ' ')
    .toLowerCase();

  // Common topic patterns in filenames
  const topicPatterns = [
    // Technology topics
    { pattern: /machine\s*learning|ml/i, topic: 'machine learning' },
    { pattern: /artificial\s*intelligence|ai/i, topic: 'artificial intelligence' },
    { pattern: /deep\s*learning/i, topic: 'deep learning' },
    { pattern: /neural\s*network/i, topic: 'neural networks' },
    { pattern: /data\s*science/i, topic: 'data science' },
    { pattern: /python|programming/i, topic: 'programming' },
    { pattern: /algorithm/i, topic: 'algorithms' },

    // Business topics
    { pattern: /business|strategy/i, topic: 'business strategy' },
    { pattern: /marketing/i, topic: 'marketing' },
    { pattern: /finance|financial/i, topic: 'finance' },
    { pattern: /management/i, topic: 'management' },
    { pattern: /sales/i, topic: 'sales' },
    { pattern: /hr|human\s*resource|policy|policies/i, topic: 'HR policies' },

    // Academic topics
    { pattern: /research|study/i, topic: 'research' },
    { pattern: /analysis|analytics/i, topic: 'analysis' },
    { pattern: /report/i, topic: 'reports' },
    { pattern: /guide|tutorial/i, topic: 'guides and tutorials' },
    { pattern: /manual|documentation/i, topic: 'documentation' },

    // General topics
    { pattern: /health|medical/i, topic: 'health and medical information' },
    { pattern: /legal|law/i, topic: 'legal information' },
    { pattern: /education|learning/i, topic: 'education' },
    { pattern: /technology|tech/i, topic: 'technology' }
  ];

  // Check for topic patterns
  topicPatterns.forEach(({ pattern, topic }) => {
    if (pattern.test(cleanName)) {
      topics.push(topic);
    }
  });

  // If no specific topics found, use cleaned filename as topic
  if (topics.length === 0 && cleanName.length > 0) {
    // Convert to title case and use as topic
    const titleCase = cleanName.replace(/\b\w/g, l => l.toUpperCase());
    if (titleCase.length <= 50) { // Only if reasonable length
      topics.push(titleCase);
    }
  }

  return topics;
}

/**
 * Extract topics from document content (first 1000 characters)
 * @param {string} content - Document content
 * @returns {Array} Extracted topics
 */
function extractTopicsFromContent(content) {
  const topics = [];

  if (!content || content.length < 50) return topics;

  // Use first 1000 characters for topic detection
  const sample = content.substring(0, 1000).toLowerCase();

  // Topic detection patterns
  const contentPatterns = [
    { pattern: /machine\s+learning|ml\s+algorithm/i, topic: 'machine learning' },
    { pattern: /artificial\s+intelligence|ai\s+system/i, topic: 'artificial intelligence' },
    { pattern: /deep\s+learning|neural\s+network/i, topic: 'deep learning and neural networks' },
    { pattern: /data\s+science|data\s+analysis/i, topic: 'data science' },
    { pattern: /business\s+strategy|strategic\s+planning/i, topic: 'business strategy' },
    { pattern: /financial\s+analysis|finance/i, topic: 'financial analysis' },
    { pattern: /marketing\s+strategy|digital\s+marketing/i, topic: 'marketing' },
    { pattern: /software\s+development|programming/i, topic: 'software development' },
    { pattern: /project\s+management|management/i, topic: 'project management' },
    { pattern: /research\s+methodology|scientific\s+research/i, topic: 'research methodology' }
  ];

  contentPatterns.forEach(({ pattern, topic }) => {
    if (pattern.test(sample)) {
      topics.push(topic);
    }
  });

  return topics;
}

/**
 * Extract topics from document metadata
 * @param {Object} metadata - Document metadata
 * @returns {Array} Extracted topics
 */
function extractTopicsFromMetadata(metadata) {
  const topics = [];

  // Check common metadata fields
  const metadataFields = ['title', 'subject', 'description', 'keywords', 'category'];

  metadataFields.forEach(field => {
    if (metadata[field] && typeof metadata[field] === 'string') {
      const fieldTopics = extractTopicsFromFilename(metadata[field]);
      topics.push(...fieldTopics);
    }
  });

  return topics;
}

/**
 * Stream chat response
 */
async function streamChatResponse(res, query, context, sessionId, requestStartTime, cacheService, documentsLength = 0, refinementData = null) {
  try {
    const openRouterService = require('../services/openRouterService');

    // Set headers for streaming
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');

    // Send initial session info
    res.write(`data: ${JSON.stringify({
      type: 'session',
      sessionId,
      timestamp: new Date().toISOString()
    })}\n\n`);

    // Check if no documents available - return static message
    if (documentsLength === 0) {
      const staticResponse = "Sorry, I don't have any information regarding this";
      const totalDuration = Date.now() - requestStartTime;

      // Send static response as content
      res.write(`data: ${JSON.stringify({
        type: 'content',
        content: staticResponse
      })}\n\n`);

      // Add to conversation history
      cacheService.addConversationEntry(sessionId, query, staticResponse, {
        contextLength: 0,
        totalDuration: totalDuration,
        staticResponse: true,
        semanticRefinement: refinementData || {
          applied: false,
          refinements: 0,
          processingTime: 0
        }
      });

      // Send completion signal
      res.write(`data: ${JSON.stringify({
        type: 'done',
        timestamp: new Date().toISOString(),
        timing: { total: totalDuration }
      })}\n\n`);

      res.end();
      return;
    }

    // Get conversation history for context
    const conversationHistory = cacheService.getFormattedConversationHistory(sessionId);

    // Stream response from OpenRouter with conversation history and refinement info
    const streamGenerator = openRouterService.generateStreamingResponse(query, context, conversationHistory, refinementData);
    let fullResponse = '';

    for await (const chunk of streamGenerator) {
      fullResponse += chunk;
      res.write(`data: ${JSON.stringify({
        type: 'content',
        content: chunk
      })}\n\n`);
    }

    const totalDuration = Date.now() - requestStartTime;

    // Add to conversation history
    cacheService.addConversationEntry(sessionId, query, fullResponse, {
      contextLength: context.length,
      totalDuration: totalDuration,
      conversationHistoryLength: conversationHistory.length,
      semanticRefinement: refinementData || {
        applied: false,
        refinements: 0,
        processingTime: 0
      }
    });

    // Send completion signal
    res.write(`data: ${JSON.stringify({
      type: 'done',
      timestamp: new Date().toISOString(),
      timing: { total: totalDuration }
    })}\n\n`);

    res.end();

  } catch (error) {
    console.error('❌ Streaming error:', error.message);
    res.write(`data: ${JSON.stringify({
      type: 'error',
      message: 'An error occurred while generating the response'
    })}\n\n`);
    res.end();
  }
}

/**
 * Main API endpoint: /api/v1/
 * ONLY endpoint needed for ChatAI functionality
 * URL format: http://localhost:3002/api/v1/?apikey=test_api_key_1751884336144_vp9gospvg&query=invoice amount
 */
router.get('/api/v1/', async (req, res) => {
  const { apikey, query, sessionId, stream = 'true', testMode = 'false' } = req.query;

  // Test mode for vector database integration testing
  const isTestMode = testMode === 'true';
  if (isTestMode) {
    console.log(`\n🧪 ═══════════════════════════════════════════════════════════════`);
    console.log(`🧪 TEST MODE ACTIVATED - BYPASSING USER SERVICE`);
    console.log(`🧪 Query: "${query}"`);
    console.log(`🧪 ═══════════════════════════════════════════════════════════════\n`);
  }

  // Validate required parameters
  if (!apikey) {
    return res.status(400).json({
      error: true,
      message: 'API key is required'
    });
  }

  if (!query) {
    return res.status(400).json({
      error: true,
      message: 'Query is required'
    });
  }

  const streamBool = stream === 'true' || stream === true;

  try {
    const userService = require('../services/userService');
    const origin = req.headers.origin || req.headers.referer || 'unknown';

    // Start timing
    const requestStartTime = Date.now();
    console.log(`⏱️ [TIMING] Request started at: ${new Date().toISOString()}`);

    // Step 1: Check cached API key validation first (or use test mode)
    const cacheService = require('../services/cacheService');

    let validationResult;
    let userServiceDuration = 0;
    let appId, chatAiId;
    let chatAiData; // Declare chatAiData in broader scope

    if (isTestMode) {
      // Test mode: Use mock validation data
      console.log('🧪 Using test mode - mock validation data');
      validationResult = {
        appId: 'test-app-123',
        id: 'test-chatai-456',
        isValid: true,
        documents: [] // Add empty documents array for test mode
      };
      chatAiData = validationResult;
      appId = validationResult.appId;
      chatAiId = validationResult.id;
      console.log(`🧪 Mock validation: AppId: ${appId}, ChatAiId: ${chatAiId}`);
    } else {
      console.log('🔑 Checking API key validation cache...');

      validationResult = cacheService.getCachedApiKeyValidation(apikey);

      if (!validationResult) {
        // Cache miss - call User Service key-validator (which now includes documents!)
        console.log(`🔄 USER SERVICE CALL - CACHE MISS - API KEY VALIDATION`);

        const userServiceStartTime = Date.now();
        validationResult = await userService.validateApiKey(apikey, origin);
        userServiceDuration = Date.now() - userServiceStartTime;

        console.log(`✅ USER SERVICE SUCCESS: API key validation completed in ${userServiceDuration}ms`);

        // Cache the validation result
        cacheService.cacheApiKeyValidation(apikey, { result: validationResult });
      } else {
        console.log(`⚡ Using cached API key validation (saved ~200-500ms)`);
        validationResult = validationResult.result;
      }

      // Extract data from validation result
      chatAiData = validationResult;
      appId = chatAiData.appId;
      chatAiId = chatAiData.id;

      if (!appId || !chatAiId) {
        throw new Error('Invalid API key: missing appId or chatAiId');
      }

      console.log(`✅ API key validated successfully. AppId: ${appId}, ChatAiId: ${chatAiId}`);
    }

    // Step 2: Credit Deduction (BEFORE expensive processing)
    if (!isTestMode) {
      console.log(`\n💰 ═══════════════════════════════════════════════════════════════`);
      console.log(`💰 STEP 2: CREDIT DEDUCTION FOR QUERY`);
      console.log(`💰 ChatAI ID: ${chatAiId}`);
      console.log(`💰 Query: "${query}"`);
      console.log(`💰 ═══════════════════════════════════════════════════════════════\n`);

      const databaseService = require('../services/databaseService');

      // Get userId from database using chatAiId (since API key validation doesn't return userId)
      let userId;
      try {
        const dataSource = databaseService.getDataSource();
        const chatAiRepo = dataSource.getRepository('ChatAi');
        const chatAiRecord = await chatAiRepo.findOne({
          where: { id: chatAiId },
          select: ['userId'],
        });

        if (!chatAiRecord) {
          console.log(`❌ ChatAI project not found: ${chatAiId}`);
          return res.status(404).json({
            error: true,
            message: 'ChatAI project not found',
          });
        }

        userId = chatAiRecord.userId;
        console.log(`👤 User ID retrieved: ${userId}`);

      } catch (error) {
        console.error(`❌ Failed to get user ID: ${error.message}`);
        return res.status(500).json({
          error: true,
          message: 'Failed to validate user permissions',
        });
      }

      const creditResult = await databaseService.deductQueryCredits(
        chatAiId,
        userId,
        1, // 1 credit per query
        {
          queryType: 'chat_query',
          sessionId: sessionId,
          query: query,
          timestamp: new Date().toISOString(),
        }
      );

      if (!creditResult.success) {
        console.log(`❌ Credit deduction failed: ${creditResult.message}`);

        return res.status(403).json({
          error: true,
          message: creditResult.message,
          creditsRemaining: creditResult.creditsRemaining,
          subscriptionStatus: creditResult.subscriptionStatus,
          upgradeMessage: creditResult.subscriptionStatus === 'free'
            ? 'Upgrade to Pro for unlimited queries!'
            : undefined,
        });
      }

      console.log(`✅ Credits deducted successfully!`);
      console.log(`💰 Remaining credits: ${creditResult.creditsRemaining}`);
      console.log(`👑 Subscription: ${creditResult.subscriptionStatus}${creditResult.isPremium ? ' (Premium)' : ''}`);
    } else {
      console.log(`🧪 Test mode: Skipping credit deduction`);
    }

    // Step 3: Import required services
    const vectorSearchService = require('../services/vectorSearchService');
    const openRouterService = require('../services/openRouterService');

    // Step 4: Get or create session using appId
    const currentSessionId = cacheService.getOrCreateSession(appId, sessionId);

    // Step 5: Use documents from key-validator response (or mock data in test mode)
    let documents;

    if (isTestMode) {
      // Test mode: Use mock documents
      documents = [
        {
          id: 1,
          filename: 'chatai-integration-guide.txt',
          parsedText: 'ChatAI Vector Database Integration Guide. This comprehensive guide explains how to integrate Qdrant vector database with the ChatAI platform. The system supports semantic search, document retrieval, and tenant isolation using appId for secure multi-client deployments. Vector databases enable efficient similarity search using embeddings generated from document content.',
          appId: 'test-app-123'
        },
        {
          id: 2,
          filename: 'vector-search-documentation.txt',
          parsedText: 'Vector Search Documentation for ChatAI Platform. This document covers the implementation of vector search capabilities using Qdrant database. Features include automatic document chunking, embedding generation, similarity search, and context retrieval for AI chat responses. The system maintains strict tenant isolation to ensure data security.',
          appId: 'test-app-123'
        },
        {
          id: 3,
          filename: 'api-reference.txt',
          parsedText: 'ChatAI API Reference Documentation. This reference guide covers all API endpoints, authentication methods, and integration patterns. The main endpoint /api/v1/ supports query processing with vector database context retrieval. Parameters include apikey for authentication, query for user questions, and optional testMode for development.',
          appId: 'test-app-123'
        }
      ];
      console.log(`🧪 Using mock documents for testing: ${documents.length} documents available`);
    } else {
      documents = chatAiData.documents || [];
      console.log(`📄 Using documents from key-validator response: ${documents.length} documents available`);
      console.log(`🔍 DEBUG: chatAiData.documents = ${JSON.stringify(chatAiData.documents, null, 2)}`);
      console.log(`⚡ OPTIMIZATION: Saved 1 API call by including documents in key-validator response`);
    }

    // Cache the documents for future requests in this session
    cacheService.cacheDocuments(currentSessionId, appId, documents, null);

    // Step 5: Semantic Refinement - Preprocess the user query
    const preprocessStartTime = Date.now();
    console.log(`\n🧠 ═══════════════════════════════════════════════════════════════`);
    console.log(`🧠 STEP 5: SEMANTIC QUERY REFINEMENT`);
    console.log(`🧠 Original query: "${query}"`);

    // Get conversation history for context enhancement
    const conversationHistory = cacheService.getFormattedConversationHistory(currentSessionId);

    // Context metadata for refinement
    const contextMetadata = {
      appId,
      documentCount: documents.length,
      documentTypes: documents.map(d => d.type || 'unknown'),
      domain: chatAiData?.domain || 'general'
    };

    // Apply semantic refinement
    const refinementResult = await promptPreprocessor.refineQuery(
      query,
      conversationHistory,
      contextMetadata,
      currentSessionId
    );

    // Use the refined query for vector search
    const refinedQuery = refinementResult.refined;
    const preprocessDuration = Date.now() - preprocessStartTime;

    console.log(`🧠 Refined query: "${refinedQuery}"`);
    console.log(`🧠 Refinements applied: ${refinementResult.refinements.length}`);
    console.log(`🧠 Processing time: ${preprocessDuration}ms`);
    console.log(`🧠 ═══════════════════════════════════════════════════════════════\n`);

    // Step 6: Get context from Qdrant Vector Database using documents
    const vectorSearchStartTime = Date.now();
    let finalContext = '';
    let vectorSearchDuration = 0;

    console.log(`\n🔍 ═══════════════════════════════════════════════════════════════`);
    console.log(`🔍 STEP 6: VECTOR DATABASE CONTEXT RETRIEVAL`);
    console.log(`🔍 Documents available: ${documents.length}`);
    console.log(`🔍 AppId: ${appId}`);
    console.log(`🔍 Original query: "${query}"`);
    console.log(`🔍 Refined query: "${refinedQuery}"`);
    console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

    if (documents.length > 0) {
      // Check cache using refined query for better cache hits
      const cachedContext = cacheService.getCachedContext(currentSessionId, refinedQuery);

      if (cachedContext) {
        finalContext = cachedContext;
        console.log(`⚡ Using cached context (saved ~1-3s)`);
        console.log(`📝 Cached context length: ${finalContext.length} characters`);
      } else {
        console.log(`🔍 Retrieving context from Qdrant Vector Database for ${documents.length} documents...`);
        console.log(`📄 Document details:`);
        documents.forEach((doc, index) => {
          console.log(`   ${index + 1}. ${doc.filename || 'Unknown'} (ID: ${doc.id})`);
        });

        // Use refined query for better vector search results
        finalContext = await vectorSearchService.retrieveFromMultipleDocuments(documents, refinedQuery, appId);
        vectorSearchDuration = Date.now() - vectorSearchStartTime;

        // Cache the context using refined query
        cacheService.cacheContext(currentSessionId, refinedQuery, finalContext);
        console.log(`✅ Context retrieved from vector database and cached in ${vectorSearchDuration}ms`);
        console.log(`📝 Final context length: ${finalContext.length} characters`);
      }
    } else {
      console.log(`⚠️ No documents available for context retrieval - returning static message`);

      // Return static message when no documents are available
      const staticResponse = "Sorry, I don't have any information regarding this";
      const totalDuration = Date.now() - requestStartTime;

      // Add to conversation history
      cacheService.addConversationEntry(currentSessionId, query, staticResponse, {
        documentsUsed: 0,
        contextLength: 0,
        cached: {
          apiKey: userServiceDuration === 0,
          context: false
        },
        staticResponse: true,
        semanticRefinement: {
          applied: refinementResult.refinements.length > 0,
          refinements: refinementResult.refinements.length,
          processingTime: preprocessDuration
        }
      });

      console.log(`⏱️ [TIMING] Static response completed in: ${totalDuration}ms`);

      return res.json({
        error: false,
        sessionId: currentSessionId,
        response: staticResponse
      });
    }

    // Step 7: Generate response
    console.log('\n🔍 ═══════════════════════════════════════════════════════════════');
    console.log('🔍 FINAL CONTEXT BEING SENT TO OPENROUTER');
    console.log('🔍 ═══════════════════════════════════════════════════════════════');
    console.log(`📝 Original Query: "${query}"`);
    console.log(`🧠 Refined Query: "${refinedQuery}"`);
    console.log(`📄 Final Context Length: ${finalContext.length} characters`);
    console.log(`📊 Documents Used: ${documents.length}`);
    console.log(`🧠 Refinements Applied: ${refinementResult.refinements.length}`);
    console.log('\n📋 FINAL CONTEXT CONTENT:');
    console.log('─'.repeat(80));
    console.log(finalContext);
    console.log('─'.repeat(80));
    console.log('🔍 ═══════════════════════════════════════════════════════════════\n');

    // Check if context is empty or insufficient (out-of-knowledge-base query)
    if (!finalContext || finalContext.trim().length === 0) {
      console.log(`⚠️ No relevant context found for query - this appears to be outside the knowledge base`);

      // Generate dynamic response based on available documents
      const outOfContextResponse = generateDynamicOutOfContextResponse(documents);
      const totalDuration = Date.now() - requestStartTime;

      // Add to conversation history
      cacheService.addConversationEntry(currentSessionId, query, outOfContextResponse, {
        documentsUsed: documents.length,
        contextLength: 0,
        outOfContext: true,
        cached: {
          apiKey: userServiceDuration === 0,
          context: vectorSearchDuration === 0
        },
        semanticRefinement: {
          applied: refinementResult.refinements.length > 0,
          refinements: refinementResult.refinements.length,
          processingTime: preprocessDuration
        }
      });

      console.log(`⏱️ [TIMING] Out-of-context response completed in: ${totalDuration}ms`);

      if (streamBool) {
        // Handle streaming response for out-of-context
        res.write(`data: ${JSON.stringify({
          type: 'session',
          sessionId: currentSessionId,
          timestamp: new Date().toISOString()
        })}\n\n`);

        res.write(`data: ${JSON.stringify({
          type: 'content',
          content: outOfContextResponse
        })}\n\n`);

        res.write(`data: ${JSON.stringify({
          type: 'done',
          timestamp: new Date().toISOString(),
          timing: { total: totalDuration },
          outOfContext: true
        })}\n\n`);

        res.end();
        return;
      } else {
        return res.json({
          error: false,
          sessionId: currentSessionId,
          response: outOfContextResponse,
          outOfContext: true
        });
      }
    }

    // Context is available - proceed with normal response generation
    if (streamBool) {
      const refinementData = {
        applied: refinementResult.refinements.length > 0,
        refinements: refinementResult.refinements.length,
        processingTime: preprocessDuration,
        originalQuery: query,
        refinedQuery: refinedQuery
      };
      await streamChatResponse(res, query, finalContext, currentSessionId, requestStartTime, cacheService, documents.length, refinementData);
    } else {
      // Get conversation history for context
      const conversationHistory = cacheService.getFormattedConversationHistory(currentSessionId);

      const openRouterStartTime = Date.now();

      // Prepare refinement info for OpenRouter logging
      const refinementInfo = {
        originalQuery: query,
        refinedQuery: refinedQuery,
        refinementsCount: refinementResult.refinements.length,
        processingTime: preprocessDuration,
        refinements: refinementResult.refinements
      };

      const response = await openRouterService.generateResponse(refinedQuery, finalContext, conversationHistory, refinementInfo);
      const openRouterDuration = Date.now() - openRouterStartTime;

      // Add to conversation history
      cacheService.addConversationEntry(currentSessionId, query, response, {
        documentsUsed: documents.length,
        contextLength: finalContext.length,
        conversationHistoryLength: conversationHistory.length,
        cached: {
          apiKey: userServiceDuration === 0,
          context: vectorSearchDuration === 0
        },
        semanticRefinement: {
          applied: refinementResult.refinements.length > 0,
          refinements: refinementResult.refinements.length,
          processingTime: preprocessDuration,
          originalQuery: query,
          refinedQuery: refinedQuery
        }
      });

      const totalDuration = Date.now() - requestStartTime;
      console.log(`⏱️ [TIMING] Total request completed in: ${totalDuration}ms`);

      res.json({
        error: false,
        sessionId: currentSessionId,
        response
      });
    }

  } catch (error) {
    console.error('❌ API v1 chat error:', error.message);

    // Return error in JSON format if headers not sent yet
    if (!res.headersSent) {
      const statusCode = error.message.includes('validation failed') ? 403 :
        error.message.includes('not found') ? 404 : 500;

      return res.status(statusCode).json({
        error: true,
        message: error.message
      });
    }
  }
});

// Test endpoint for vector search integration (without User Service dependency)
router.get('/test-vector', async (req, res) => {
  try {
    const { query = 'test query' } = req.query;
    const vectorSearchService = require('../services/vectorSearchService');

    // Mock documents for testing
    const mockDocuments = [
      {
        id: 1,
        filename: 'test-document.txt',
        parsedText: 'ChatAI Vector Database Integration Test Document. This document contains information about vector databases, semantic search, and ChatAI platform integration. Vector databases enable efficient similarity search using embeddings.',
        appId: 'test-app-123'
      },
      {
        id: 2,
        filename: 'integration-guide.txt',
        parsedText: 'Integration Guide for ChatAI Platform. This guide explains how to integrate vector databases with the ChatAI SDK. The system supports tenant isolation using appId for secure multi-client deployments.',
        appId: 'test-app-123'
      }
    ];

    console.log(`🧪 Testing vector search with query: "${query}"`);

    // Test vector search service
    const context = await vectorSearchService.retrieveFromMultipleDocuments(
      mockDocuments,
      query,
      'test-app-123'
    );

    // Get vector database stats
    const stats = await vectorSearchService.getStats();

    res.json({
      status: 'success',
      test: 'Vector Search Integration',
      query: query,
      documentsProcessed: mockDocuments.length,
      contextGenerated: {
        length: context.length,
        preview: context.substring(0, 200) + (context.length > 200 ? '...' : '')
      },
      vectorDatabase: {
        status: stats.status || 'not_available',
        stats: stats
      },
      integration: {
        service: 'Qdrant Vector Database',
        fallbackMode: stats.status === 'error',
        tenantIsolation: 'appId-based'
      }
    });

  } catch (error) {
    console.error('❌ Test vector endpoint error:', error.message);
    res.status(500).json({
      status: 'error',
      test: 'Vector Search Integration',
      error: error.message
    });
  }
});

// Health check endpoint
router.get('/health', async (req, res) => {
  try {
    const vectorSearchService = require('../services/vectorSearchService');

    // Check vector database status
    let vectorDbStatus = 'unknown';
    let vectorDbStats = {};

    try {
      vectorDbStats = await vectorSearchService.getStats();
      vectorDbStatus = vectorDbStats.status === 'green' ? 'healthy' : 'degraded';
    } catch (error) {
      vectorDbStatus = 'error';
      vectorDbStats = { error: error.message };
    }

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'ChatAI SDK Clean with Qdrant Vector Database',
      endpoints: {
        main: '/api/v1/?apikey=...&query=...',
        health: '/health'
      },
      vectorDatabase: {
        status: vectorDbStatus,
        stats: vectorDbStats
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      service: 'ChatAI SDK Clean',
      error: error.message
    });
  }
});

// Mount vector processing routes
router.use('/api/vector', vectorProcessingRoutes);

module.exports = router;
