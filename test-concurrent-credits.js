#!/usr/bin/env node

/**
 * Test script for concurrent credit deduction
 * This script simulates multiple users making simultaneous queries
 * to test the atomic credit deduction system
 */

const fetch = require('node-fetch');

// Configuration
const BASE_URL = 'http://localhost:3000';
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.CtI6e0pRgdgSqXyhD18xAdTW_uDLe8OgY_uuvz1_ZaI';
const CHATAI_ID = 'a9a30924-f27e-4c7d-8d6f-3f0dd2da04ce';

// Test configuration
const CONCURRENT_REQUESTS = 10; // Number of simultaneous requests
const CREDITS_PER_REQUEST = 1;   // Credits to deduct per request

/**
 * Get current credit balance
 */
async function getCurrentCredits() {
  try {
    const response = await fetch(`${BASE_URL}/users/app/chatai/get-single-chatai?appId=4f88077b-7567-4ca5-836a-115f2d7a333e`, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Accept': '*/*'
      }
    });
    
    const data = await response.json();
    return data.result?.credits || 0;
  } catch (error) {
    console.error('Error getting credits:', error.message);
    return null;
  }
}

/**
 * Make a single credit deduction request
 */
async function deductCredits(requestId) {
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${BASE_URL}/users/app/chatai/test/deduct-query-credits`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json',
        'Accept': '*/*'
      },
      body: JSON.stringify({
        appId: CHATAI_ID,
        creditsToDeduct: CREDITS_PER_REQUEST
      })
    });
    
    const data = await response.json();
    const duration = Date.now() - startTime;
    
    return {
      requestId,
      success: !data.error,
      status: response.status,
      message: data.message,
      creditsRemaining: data.result?.creditsRemaining,
      duration,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      requestId,
      success: false,
      error: error.message,
      duration: Date.now() - startTime,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Run concurrent credit deduction test
 */
async function runConcurrentTest() {
  console.log('🧪 Starting Concurrent Credit Deduction Test');
  console.log('=' .repeat(50));
  
  // Get initial credits
  console.log('📊 Getting initial credit balance...');
  const initialCredits = await getCurrentCredits();
  if (initialCredits === null) {
    console.error('❌ Failed to get initial credits. Exiting.');
    return;
  }
  
  console.log(`💰 Initial Credits: ${initialCredits}`);
  console.log(`🚀 Launching ${CONCURRENT_REQUESTS} concurrent requests...`);
  console.log(`💳 Each request will deduct ${CREDITS_PER_REQUEST} credit(s)`);
  console.log(`🎯 Expected final credits: ${Math.max(0, initialCredits - (CONCURRENT_REQUESTS * CREDITS_PER_REQUEST))}`);
  console.log('');
  
  // Launch concurrent requests
  const startTime = Date.now();
  const promises = [];
  
  for (let i = 1; i <= CONCURRENT_REQUESTS; i++) {
    promises.push(deductCredits(i));
  }
  
  // Wait for all requests to complete
  console.log('⏳ Waiting for all requests to complete...');
  const results = await Promise.all(promises);
  const totalDuration = Date.now() - startTime;
  
  // Analyze results
  console.log('\n📈 Results Analysis:');
  console.log('=' .repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful requests: ${successful.length}/${CONCURRENT_REQUESTS}`);
  console.log(`❌ Failed requests: ${failed.length}/${CONCURRENT_REQUESTS}`);
  console.log(`⏱️  Total test duration: ${totalDuration}ms`);
  console.log(`⚡ Average request duration: ${Math.round(results.reduce((sum, r) => sum + r.duration, 0) / results.length)}ms`);
  
  // Show detailed results
  console.log('\n📋 Detailed Results:');
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    const credits = result.creditsRemaining !== undefined ? ` (${result.creditsRemaining} credits remaining)` : '';
    console.log(`${status} Request ${result.requestId}: ${result.message || result.error}${credits} [${result.duration}ms]`);
  });
  
  // Get final credits
  console.log('\n💰 Getting final credit balance...');
  const finalCredits = await getCurrentCredits();
  
  if (finalCredits !== null) {
    const actualDeduction = initialCredits - finalCredits;
    const expectedDeduction = successful.length * CREDITS_PER_REQUEST;
    
    console.log(`💰 Final Credits: ${finalCredits}`);
    console.log(`📉 Credits Deducted: ${actualDeduction} (expected: ${expectedDeduction})`);
    
    if (actualDeduction === expectedDeduction) {
      console.log('🎉 SUCCESS: Credit deduction is consistent! No race conditions detected.');
    } else {
      console.log('⚠️  WARNING: Credit deduction mismatch detected. Possible race condition.');
    }
  }
  
  // Test summary
  console.log('\n🏁 Test Summary:');
  console.log('=' .repeat(50));
  console.log(`Initial Credits: ${initialCredits}`);
  console.log(`Final Credits: ${finalCredits}`);
  console.log(`Successful Deductions: ${successful.length}`);
  console.log(`Failed Deductions: ${failed.length}`);
  console.log(`Race Condition Protection: ${actualDeduction === expectedDeduction ? 'WORKING ✅' : 'FAILED ❌'}`);
}

// Run the test
if (require.main === module) {
  runConcurrentTest().catch(console.error);
}

module.exports = { runConcurrentTest, getCurrentCredits, deductCredits };
