# compiled output
/dist
/node_modules
/reference_user-service
/CHAT-AI-SDK-Clean

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
package-lock.json

# OS
.DS_Store

# Envirnments
.env

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
ChatAI-SDK-Clean/.env.example
ChatAI-SDK-Clean/.gitignore
ChatAI-SDK-Clean/analyze-logs.js
ChatAI-SDK-Clean/demo-filtering.js
ChatAI-SDK-Clean/EMPTY_DOCUMENTS_HANDLING.md
ChatAI-SDK-Clean/example.env
ChatAI-SDK-Clean/FILTERING_LOGS_GUIDE.md
ChatAI-SDK-Clean/FILTERING_TOOLS.md
ChatAI-SDK-Clean/IMPLEMENTATION_SUMMARY.md
ChatAI-SDK-Clean/index.js
ChatAI-SDK-Clean/LLAMAPARSE_RETRY_GUIDE.md
ChatAI-SDK-Clean/LOGGING_README.md
ChatAI-SDK-Clean/OPTIMIZATION_SUMMARY.md
ChatAI-SDK-Clean/package.json
ChatAI-SDK-Clean/README.md
ChatAI-SDK-Clean/SHORT_CONTENT_SOLUTIONS.md
ChatAI-SDK-Clean/SMART_CHUNKING_GUIDE.md
ChatAI-SDK-Clean/test_chunk_filtering.js
ChatAI-SDK-Clean/test_context_optimization.js
ChatAI-SDK-Clean/test_dynamic_responses.js
ChatAI-SDK-Clean/test_embedding_cache.js
ChatAI-SDK-Clean/test_llamaparse_retry.js
ChatAI-SDK-Clean/test_optimizations.js
ChatAI-SDK-Clean/test_out_of_context.js
ChatAI-SDK-Clean/test_page_filtering.js
ChatAI-SDK-Clean/test_short_page_preservation.js
ChatAI-SDK-Clean/test_tabular_filtering.js
ChatAI-SDK-Clean/test_valuable_content_detection.js
ChatAI-SDK-Clean/test-clean.js
ChatAI-SDK-Clean/test-database-integration.js
ChatAI-SDK-Clean/test-empty-documents.js
ChatAI-SDK-Clean/test-logging.js
ChatAI-SDK-Clean/test-rate-limiting.html
ChatAI-SDK-Clean/test-rate-limiting.sh
ChatAI-SDK-Clean/test-short-content-preservation.js
ChatAI-SDK-Clean/test-smart-chunking.js
ChatAI-SDK-Clean/src/server.js
ChatAI-SDK-Clean/src/config/database.js
ChatAI-SDK-Clean/src/config/index.js
ChatAI-SDK-Clean/src/entities/ChatAi.js
ChatAI-SDK-Clean/src/entities/ChatAiDocument.js
ChatAI-SDK-Clean/src/entities/ChatAiMessage.js
ChatAI-SDK-Clean/src/middleware/rateLimit.js
ChatAI-SDK-Clean/src/routes/index.js
ChatAI-SDK-Clean/src/routes/vectorProcessing.js
ChatAI-SDK-Clean/src/services/cacheService.js
ChatAI-SDK-Clean/src/services/databaseService.js
ChatAI-SDK-Clean/src/services/embeddingService.js
ChatAI-SDK-Clean/src/services/llamaIndexService.js
ChatAI-SDK-Clean/src/services/llamaParseService.js
ChatAI-SDK-Clean/src/services/openRouterService.js
ChatAI-SDK-Clean/src/services/qdrantService.js
ChatAI-SDK-Clean/src/services/smartChunkingService.js
ChatAI-SDK-Clean/src/services/userService.js
ChatAI-SDK-Clean/src/services/vectorSearchService.js
ChatAI-SDK-Clean/src/utils/logger.js
ChatAI-SDK-Clean/test-conversation-integration.js
ChatAI-SDK-Clean/test-conversation-memory.js
ChatAI-SDK-Clean/corrupted_pdf.pdf
ChatAI-SDK-Clean/fake_document.pdf
ChatAI-SDK-Clean/SEMANTIC_REFINEMENT.md
ChatAI-SDK-Clean/test_failure.pdf
ChatAI-SDK-Clean/test_refund.pdf
ChatAI-SDK-Clean/test-semantic-refinement.js
ChatAI-SDK-Clean/src/config/semanticRefinement.js
ChatAI-SDK-Clean/src/services/promptPreprocessorService.js
ChatAI-SDK-Clean/src/services/preprocessing/contextEnhancer.js
ChatAI-SDK-Clean/src/services/preprocessing/queryAnalyzer.js
ChatAI-SDK-Clean/src/services/preprocessing/queryExpander.js
