version: "3"

services:
  # redis:
  #   image: redis:latest
  #   container_name: redis
  #   ports:
  #     - "6379:6379"

  rabbitmq:
    image: "rabbitmq:management"
    container_name: rabbitmq
    restart: unless-stopped
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: "admin"
      RABBITMQ_DEFAULT_PASS: "admin123"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

  postgres:
    image: postgres:latest
    container_name: postgres
    restart: unless-stopped
    ports:
      - "5433:5432"
    environment:
      POSTGRES_USER: "postgres"
      POSTGRES_PASSWORD: "postgres"
      POSTGRES_DB: "abstraxn"
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin
    restart: unless-stopped
    ports:
      - "5050:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: "<EMAIL>"
      PGADMIN_DEFAULT_PASSWORD: "admin"
    volumes:
      - pgadmin_data:/var/lib/pgadmin

  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant
    restart: unless-stopped
    ports:
      - "6333:6333" # HTTP API
      - "6334:6334" # gRPC API
    volumes:
      - qdrant_storage:/qdrant/storage

    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
  # mysql:
  #   image: mysql:8.0.20
  #   container_name: mysql_container
  #   restart: on-failure
  #   command: --default-authentication-plugin=mysql_native_password
  #   environment:
  #     - MYSQL_ROOT_PASSWORD=admin123
  #     - MYSQL_USER=root
  #     - MYSQL_PASSWORD=admin123
  #   volumes:
  #     - /data/dvolumes/cmnico/mysql:/var/lib/mysql
  #   ports:
  #     - "3306:3306"

  # phpmyadmin:
  #   container_name: phpmyadmin_container
  #   image: phpmyadmin/phpmyadmin:5.0.1
  #   restart: on-failure
  #   environment:
  #     PMA_HOST: mysql
  #   ports:
  #     - "8084:80"

volumes:
  postgres_data:
    driver: local
  qdrant_storage:
    driver: local
  rabbitmq_data:
    driver: local
  pgadmin_data:
    driver: local
