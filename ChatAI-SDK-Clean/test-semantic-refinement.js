/**
 * Test Script for Semantic Refinement System
 * Tests the prompt preprocessing functionality
 */

const PromptPreprocessorService = require('./src/services/promptPreprocessorService');

async function testSemanticRefinement() {
  console.log('🧠 Testing Semantic Refinement System');
  console.log('═'.repeat(60));

  const preprocessor = new PromptPreprocessorService();

  // Test cases with different types of queries
  const testCases = [
    {
      name: 'Simple Question',
      query: 'What is API?',
      conversationHistory: [],
      expected: 'Should expand API to related terms'
    },
    {
      name: 'Ambiguous Query',
      query: 'How does it work?',
      conversationHistory: [
        { role: 'user', content: 'Tell me about database connections' },
        { role: 'assistant', content: 'Database connections are...' }
      ],
      expected: 'Should resolve "it" to database connections'
    },
    {
      name: 'Incomplete Query',
      query: 'show me',
      conversationHistory: [
        { role: 'user', content: 'I need help with user authentication' },
        { role: 'assistant', content: 'User authentication involves...' }
      ],
      expected: 'Should add context about user authentication'
    },
    {
      name: 'Follow-up Query',
      query: 'Also tell me about security',
      conversationHistory: [
        { role: 'user', content: 'How to implement login system?' },
        { role: 'assistant', content: 'To implement a login system...' }
      ],
      expected: 'Should connect to previous login system topic'
    },
    {
      name: 'Technical Query',
      query: 'database error troubleshooting',
      conversationHistory: [],
      expected: 'Should expand with technical terms'
    },
    {
      name: 'Complete Query',
      query: 'How to implement user authentication with JWT tokens in Node.js?',
      conversationHistory: [],
      expected: 'Should require minimal refinement'
    }
  ];

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`\n${i + 1}. Testing: ${testCase.name}`);
    console.log(`   Original: "${testCase.query}"`);
    console.log(`   Expected: ${testCase.expected}`);

    try {
      const result = await preprocessor.refineQuery(
        testCase.query,
        testCase.conversationHistory,
        { domain: 'technical' },
        `test-session-${i}`
      );

      console.log(`   Refined:  "${result.refined}"`);
      console.log(`   Refinements: ${result.refinements.length}`);
      console.log(`   Processing time: ${result.processingTime}ms`);
      console.log(`   Cached: ${result.cached}`);

      if (result.refinements.length > 0) {
        console.log(`   Applied refinements:`);
        result.refinements.forEach((refinement, idx) => {
          console.log(`     ${idx + 1}. ${refinement.type}: ${JSON.stringify(refinement)}`);
        });
      }

      if (result.analysis) {
        console.log(`   Analysis:`);
        console.log(`     - Intent: ${result.analysis.intent?.join(', ') || 'unknown'}`);
        console.log(`     - Ambiguity: ${result.analysis.ambiguity?.toFixed(2) || 'N/A'}`);
        console.log(`     - Completeness: ${result.analysis.completeness?.toFixed(2) || 'N/A'}`);
        console.log(`     - Complexity: ${result.analysis.complexity?.toFixed(2) || 'N/A'}`);
        console.log(`     - Needs context: ${result.analysis.needsContext || false}`);
      }

    } catch (error) {
      console.error(`   ❌ Error: ${error.message}`);
    }

    console.log('   ' + '─'.repeat(50));
  }

  // Test caching
  console.log(`\n🔄 Testing Cache Functionality`);
  const cacheTestQuery = 'What is database optimization?';
  
  console.log(`First call (should not be cached):`);
  const firstResult = await preprocessor.refineQuery(cacheTestQuery, [], {}, 'cache-test');
  console.log(`   Processing time: ${firstResult.processingTime}ms, Cached: ${firstResult.cached}`);

  console.log(`Second call (should be cached):`);
  const secondResult = await preprocessor.refineQuery(cacheTestQuery, [], {}, 'cache-test');
  console.log(`   Processing time: ${secondResult.processingTime}ms, Cached: ${secondResult.cached}`);

  // Test configuration
  console.log(`\n⚙️ Testing Configuration`);
  console.log(`Current config:`);
  console.log(`   - Enabled: ${preprocessor.config.enabled}`);
  console.log(`   - AI enabled: ${preprocessor.config.ai.enabled}`);
  console.log(`   - Cache enabled: ${preprocessor.config.cache.enabled}`);
  console.log(`   - Max expansions: ${preprocessor.config.expansion.maxExpansions}`);

  // Test disabling
  console.log(`\nDisabling semantic refinement:`);
  preprocessor.updateConfig({ enabled: false });
  const disabledResult = await preprocessor.refineQuery('test query', [], {}, 'disabled-test');
  console.log(`   Result when disabled: "${disabledResult.refined}" (should be same as original)`);

  // Re-enable
  preprocessor.updateConfig({ enabled: true });

  // Cache stats
  console.log(`\n📊 Cache Statistics:`);
  const cacheStats = preprocessor.getCacheStats();
  console.log(`   - Size: ${cacheStats.size}`);
  console.log(`   - Timeout: ${cacheStats.timeout}ms`);
  console.log(`   - Enabled: ${cacheStats.enabled}`);

  console.log('\n✅ Semantic Refinement Testing Complete!');
  console.log('═'.repeat(60));
}

// Run the test
if (require.main === module) {
  testSemanticRefinement().catch(console.error);
}

module.exports = testSemanticRefinement;
