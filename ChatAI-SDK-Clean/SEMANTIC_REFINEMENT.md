# Semantic Refinement System

## Overview

The Semantic Refinement System provides intelligent preprocessing of user queries to improve retrieval accuracy and handle ambiguous queries. It combines **context-aware enhancement**, **query expansion**, and a **hybrid rule-based + AI-powered approach**.

## Features

### 🧠 Context-Aware Enhancement
- **Pronoun Resolution**: Resolves "it", "this", "that" using conversation history
- **Follow-up Detection**: Identifies and enhances follow-up queries
- **Topic Continuity**: Maintains conversation context across queries

### 🔍 Query Expansion & Clarification
- **Rule-based Expansion**: Uses predefined domain-specific terms
- **Synonym Enhancement**: Adds relevant synonyms and alternative phrasings
- **AI-powered Expansion**: Uses OpenRouter for complex semantic understanding
- **Domain-specific Terms**: Technical, business, process, and data domains

### 📊 Query Analysis
- **Intent Classification**: Question, command, comparison, etc.
- **Ambiguity Detection**: Identifies vague or unclear queries
- **Completeness Assessment**: Determines if queries need enhancement
- **Complexity Analysis**: Measures query sophistication

## Architecture

```
User Query → Query Analyzer → Context Enhancer → Query Expander → Refined Query
                ↓                    ↓                 ↓
            Analysis Data    Conversation Context   Domain Terms
```

### Core Components

1. **PromptPreprocessorService** - Main orchestrator
2. **QueryAnalyzer** - Analyzes query properties
3. **ContextEnhancer** - Adds conversation context
4. **QueryExpander** - Expands with related terms

## Integration

The system integrates seamlessly into the existing ChatSDK flow:

```javascript
// Before vector search
const refinementResult = await promptPreprocessor.refineQuery(
  originalQuery,
  conversationHistory,
  contextMetadata,
  sessionId
);

// Use refined query for better retrieval
const context = await vectorSearch(documents, refinementResult.refined, appId);
```

## Configuration

Configure via environment variables or `src/config/semanticRefinement.js`:

```javascript
// Main toggle
SEMANTIC_REFINEMENT_ENABLED=true

// AI settings
SEMANTIC_AI_ENABLED=true
SEMANTIC_AI_MODEL=openai/gpt-3.5-turbo

// Cache settings
SEMANTIC_CACHE_ENABLED=true
SEMANTIC_CACHE_TTL=1800000  // 30 minutes

// Analysis thresholds
SEMANTIC_AMBIGUITY_THRESHOLD=0.6
SEMANTIC_COMPLETENESS_THRESHOLD=0.7
```

## Examples

### Pronoun Resolution
```
Original: "How does it work?"
Context: Previous query about "database connections"
Refined: "How does database connections work?"
```

### Query Expansion
```
Original: "API error"
Refined: "API error (related to: endpoint, service, integration, debugging)"
```

### Follow-up Enhancement
```
Original: "Also show me security features"
Context: Previous query about "user authentication"
Refined: "Regarding user authentication, also show me security features"
```

### Incomplete Query Enhancement
```
Original: "show me"
Context: Previous discussion about "user management"
Refined: "show me (in the context of user management)"
```

## Performance

- **Processing Time**: Typically 50-200ms per query
- **Caching**: Refined queries cached for 30 minutes
- **Fallback**: Graceful degradation if AI services unavailable
- **Parallel Processing**: Multiple refinement steps run concurrently

## Monitoring

The system provides detailed logging and metrics:

```javascript
{
  "semanticRefinement": {
    "applied": true,
    "refinements": 3,
    "processingTime": 150,
    "originalQuery": "How does it work?",
    "refinedQuery": "How does database connections work?"
  }
}
```

## Testing

Run the test suite to verify functionality:

```bash
node test-semantic-refinement.js
```

Test cases include:
- Simple questions
- Ambiguous queries
- Incomplete queries
- Follow-up queries
- Technical queries
- Cache functionality

## Benefits

### 🎯 Better Retrieval Accuracy
- Refined queries match documents more effectively
- Reduced false negatives in vector search
- Improved context relevance

### 🤝 Enhanced User Experience
- Handles ambiguous queries gracefully
- Maintains conversation continuity
- Reduces need for query reformulation

### 🔧 Flexible Configuration
- Enable/disable individual features
- Adjust thresholds for different use cases
- Domain-specific customization

## API Reference

### PromptPreprocessorService

#### `refineQuery(originalQuery, conversationHistory, contextMetadata, sessionId)`

**Parameters:**
- `originalQuery` (string): The user's original query
- `conversationHistory` (Array): Previous conversation messages
- `contextMetadata` (Object): Additional context information
- `sessionId` (string): Session identifier for caching

**Returns:**
```javascript
{
  original: "original query",
  refined: "refined query",
  refinements: [
    {
      type: "pronoun_resolution",
      original: "it",
      resolved: "database connections",
      confidence: 0.8
    }
  ],
  analysis: {
    intent: ["question"],
    ambiguity: 0.7,
    completeness: 0.4,
    needsContext: true
  },
  cached: false,
  processingTime: 150
}
```

#### `updateConfig(newConfig)`
Updates the configuration settings.

#### `clearCache()`
Clears the refinement cache.

#### `getCacheStats()`
Returns cache statistics.

## Troubleshooting

### Common Issues

1. **High Processing Time**
   - Reduce AI model complexity
   - Increase cache TTL
   - Disable expensive features

2. **Poor Refinement Quality**
   - Adjust analysis thresholds
   - Update domain-specific terms
   - Review conversation history quality

3. **Cache Issues**
   - Check cache size limits
   - Verify TTL settings
   - Monitor memory usage

### Debug Mode

Enable detailed logging:
```javascript
SEMANTIC_LOGGING_ENABLED=true
SEMANTIC_LOG_LEVEL=debug
SEMANTIC_LOG_REFINEMENTS=true
```

## Future Enhancements

- **Semantic Similarity**: Vector-based query similarity
- **Intent Prediction**: ML-based intent classification
- **Multi-language Support**: Support for non-English queries
- **Learning System**: Adaptive refinement based on user feedback
