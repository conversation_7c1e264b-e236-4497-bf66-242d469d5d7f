/**
 * Query Expander
 * Expands user queries with related terms, synonyms, and clarifications
 * Uses both rule-based and AI-powered approaches
 */

class QueryExpander {
  constructor() {
    // Domain-specific expansion rules
    this.expansionRules = {
      // Technical terms
      'api': ['application programming interface', 'endpoint', 'service', 'integration'],
      'database': ['db', 'data storage', 'sql', 'nosql', 'records'],
      'server': ['backend', 'host', 'infrastructure', 'deployment'],
      'error': ['bug', 'issue', 'problem', 'exception', 'failure'],
      'function': ['method', 'procedure', 'routine', 'operation'],
      
      // Business terms
      'revenue': ['income', 'sales', 'earnings', 'profit'],
      'customer': ['client', 'user', 'buyer', 'consumer'],
      'strategy': ['plan', 'approach', 'methodology', 'framework'],
      'analysis': ['report', 'study', 'evaluation', 'assessment'],
      
      // Process terms
      'workflow': ['process', 'procedure', 'steps', 'methodology'],
      'implementation': ['deployment', 'execution', 'setup', 'installation'],
      'optimization': ['improvement', 'enhancement', 'tuning', 'performance'],
      
      // Common abbreviations
      'ai': ['artificial intelligence', 'machine learning', 'ml'],
      'ui': ['user interface', 'frontend', 'interface'],
      'ux': ['user experience', 'usability', 'design']
    };

    // Synonym groups for expansion
    this.synonymGroups = {
      'find': ['search', 'locate', 'discover', 'identify'],
      'show': ['display', 'present', 'demonstrate', 'exhibit'],
      'create': ['make', 'build', 'generate', 'develop'],
      'fix': ['repair', 'resolve', 'correct', 'solve'],
      'improve': ['enhance', 'optimize', 'upgrade', 'better']
    };

    // Question expansion templates
    this.questionTemplates = {
      'what': ['what is', 'what are', 'what does', 'what can'],
      'how': ['how to', 'how do', 'how can', 'how does'],
      'why': ['why is', 'why do', 'why does', 'why should'],
      'when': ['when to', 'when do', 'when should', 'when can']
    };
  }

  /**
   * Expand query with related terms and clarifications
   * @param {string} query - The query to expand
   * @param {Object} analysis - Query analysis results
   * @param {Object} contextMetadata - Additional context
   * @param {boolean} useAI - Whether to use AI for expansion
   * @returns {Object} Expansion results
   */
  async expandQuery(query, analysis = {}, contextMetadata = {}, useAI = true) {
    const expansions = [];
    const refinements = [];

    try {
      console.log(`🔍 Expanding query: "${query}"`);

      // Rule-based expansion
      const ruleBasedExpansions = this.applyRuleBasedExpansion(query, analysis);
      expansions.push(...ruleBasedExpansions.expansions);
      refinements.push(...ruleBasedExpansions.refinements);

      // Synonym expansion
      const synonymExpansions = this.applySynonymExpansion(query);
      expansions.push(...synonymExpansions.expansions);
      refinements.push(...synonymExpansions.refinements);

      // Domain-specific expansion
      const domainExpansions = this.applyDomainExpansion(query, analysis.domain || []);
      expansions.push(...domainExpansions.expansions);
      refinements.push(...domainExpansions.refinements);

      // AI-powered expansion (if enabled and needed)
      if (useAI && this.shouldUseAIExpansion(analysis)) {
        const aiExpansions = await this.applyAIExpansion(query, analysis, contextMetadata);
        expansions.push(...aiExpansions.expansions);
        refinements.push(...aiExpansions.refinements);
      }

      // Remove duplicates and rank by relevance
      const uniqueExpansions = this.rankAndFilterExpansions(expansions, query);

      console.log(`✨ Query expansion completed: ${uniqueExpansions.length} expansions found`);

      return {
        expansions: uniqueExpansions,
        refinements,
        methods: ['rule_based', 'synonym', 'domain', ...(useAI ? ['ai'] : [])]
      };

    } catch (error) {
      console.error(`❌ Error in query expansion:`, error);
      return {
        expansions: [],
        refinements: [],
        error: error.message
      };
    }
  }

  /**
   * Apply rule-based expansion using predefined rules
   */
  applyRuleBasedExpansion(query, analysis) {
    const expansions = [];
    const refinements = [];
    const queryLower = query.toLowerCase();

    // Expand based on expansion rules
    for (const [term, relatedTerms] of Object.entries(this.expansionRules)) {
      if (queryLower.includes(term)) {
        expansions.push(...relatedTerms);
        refinements.push({
          type: 'rule_based_expansion',
          originalTerm: term,
          expansions: relatedTerms,
          confidence: 0.8
        });
      }
    }

    // Expand incomplete questions
    if (analysis.queryType === 'question' && analysis.completeness < 0.7) {
      const questionWord = this.extractQuestionWord(query);
      if (questionWord && this.questionTemplates[questionWord]) {
        const templates = this.questionTemplates[questionWord];
        expansions.push(...templates);
        refinements.push({
          type: 'question_expansion',
          questionWord,
          templates,
          confidence: 0.7
        });
      }
    }

    return { expansions, refinements };
  }

  /**
   * Apply synonym expansion
   */
  applySynonymExpansion(query) {
    const expansions = [];
    const refinements = [];
    const queryLower = query.toLowerCase();

    for (const [word, synonyms] of Object.entries(this.synonymGroups)) {
      if (queryLower.includes(word)) {
        expansions.push(...synonyms);
        refinements.push({
          type: 'synonym_expansion',
          originalWord: word,
          synonyms,
          confidence: 0.6
        });
      }
    }

    return { expansions, refinements };
  }

  /**
   * Apply domain-specific expansion
   */
  applyDomainExpansion(query, domains) {
    const expansions = [];
    const refinements = [];

    // Domain-specific term expansion
    const domainTerms = {
      'technical': ['implementation', 'configuration', 'debugging', 'testing'],
      'business': ['requirements', 'stakeholders', 'deliverables', 'metrics'],
      'process': ['workflow', 'automation', 'efficiency', 'optimization'],
      'data': ['analytics', 'visualization', 'insights', 'trends']
    };

    for (const domain of domains) {
      if (domainTerms[domain]) {
        expansions.push(...domainTerms[domain]);
        refinements.push({
          type: 'domain_expansion',
          domain,
          terms: domainTerms[domain],
          confidence: 0.7
        });
      }
    }

    return { expansions, refinements };
  }

  /**
   * Apply AI-powered expansion using OpenRouter
   */
  async applyAIExpansion(query, analysis, contextMetadata) {
    const expansions = [];
    const refinements = [];

    try {
      // Only use AI for complex or ambiguous queries
      if (analysis.complexity > 0.6 || analysis.ambiguity > 0.7) {
        const openRouterService = require('../openRouterService');
        
        const expansionPrompt = this.buildExpansionPrompt(query, analysis, contextMetadata);
        
        // Use a lightweight model for expansion to save costs
        const originalModel = openRouterService.model;
        openRouterService.model = 'openai/gpt-3.5-turbo'; // Use cheaper model for expansion
        
        const response = await openRouterService.generateResponse(expansionPrompt, '', []);
        
        // Restore original model
        openRouterService.model = originalModel;
        
        // Parse AI response for expansion terms
        const aiExpansions = this.parseAIExpansionResponse(response);
        expansions.push(...aiExpansions);
        
        refinements.push({
          type: 'ai_expansion',
          prompt: expansionPrompt,
          response: response.substring(0, 100) + '...',
          expansions: aiExpansions,
          confidence: 0.8
        });
      }
    } catch (error) {
      console.error(`❌ AI expansion failed:`, error);
      // Fail gracefully - don't throw error
    }

    return { expansions, refinements };
  }

  /**
   * Build prompt for AI expansion
   */
  buildExpansionPrompt(query, analysis, contextMetadata) {
    return `Given this user query: "${query}"

Please provide 3-5 related terms or phrases that would help improve search results. Focus on:
- Synonyms and alternative phrasings
- Related concepts and terminology
- Domain-specific terms if applicable

Query analysis:
- Intent: ${analysis.intent?.join(', ') || 'unknown'}
- Domain: ${analysis.domain?.join(', ') || 'general'}
- Ambiguity: ${analysis.ambiguity || 0}

Respond with only the related terms, separated by commas. No explanations.`;
  }

  /**
   * Parse AI response for expansion terms
   */
  parseAIExpansionResponse(response) {
    try {
      // Extract terms from AI response
      const terms = response
        .split(/[,\n]/)
        .map(term => term.trim())
        .filter(term => term.length > 2 && term.length < 50)
        .slice(0, 5); // Limit to 5 terms

      return terms;
    } catch (error) {
      console.error(`❌ Error parsing AI expansion response:`, error);
      return [];
    }
  }

  /**
   * Determine if AI expansion should be used
   */
  shouldUseAIExpansion(analysis) {
    // Use AI for complex, ambiguous, or incomplete queries
    return (
      analysis.complexity > 0.6 ||
      analysis.ambiguity > 0.7 ||
      analysis.completeness < 0.5
    );
  }

  /**
   * Rank and filter expansions by relevance
   */
  rankAndFilterExpansions(expansions, originalQuery) {
    // Remove duplicates
    const unique = [...new Set(expansions)];
    
    // Filter out expansions that are too similar to original query
    const filtered = unique.filter(expansion => {
      const similarity = this.calculateSimilarity(expansion.toLowerCase(), originalQuery.toLowerCase());
      return similarity < 0.8; // Remove if too similar
    });
    
    // Sort by length (prefer shorter, more focused terms)
    const sorted = filtered.sort((a, b) => a.length - b.length);
    
    // Return top 5 most relevant
    return sorted.slice(0, 5);
  }

  /**
   * Calculate similarity between two strings (simple implementation)
   */
  calculateSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * Calculate Levenshtein distance
   */
  levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * Extract question word from query
   */
  extractQuestionWord(query) {
    const questionWords = ['what', 'how', 'why', 'when', 'where', 'who', 'which'];
    const queryLower = query.toLowerCase();
    
    for (const word of questionWords) {
      if (queryLower.startsWith(word)) {
        return word;
      }
    }
    
    return null;
  }
}

module.exports = QueryExpander;
